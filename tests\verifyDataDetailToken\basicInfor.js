const axios = require('axios');

// === CONFIGURATION ===
const CONFIG = {
    TOKEN_ADDRESS: '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao',
    TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000 // 1 second
};

// === API CLIENTS ===
class APIClient {
    constructor(baseURL, defaultHeaders = {}) {
        this.client = axios.create({
            baseURL,
            timeout: CONFIG.TIMEOUT,
            headers: defaultHeaders
        });
    }

    async request(config, retries = CONFIG.MAX_RETRIES) {
        try {
            const response = await this.client(config);
            return response.data;
        } catch (error) {
            if (retries > 0 && this.shouldRetry(error)) {
                console.warn(`🔄 Retrying request... (${CONFIG.MAX_RETRIES - retries + 1}/${CONFIG.MAX_RETRIES})`);
                await this.delay(CONFIG.RETRY_DELAY);
                return this.request(config, retries - 1);
            }
            throw error;
        }
    }

    shouldRetry(error) {
        return error.code === 'ECONNABORTED' ||
               (error.response && [429, 500, 502, 503, 504].includes(error.response.status));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Solscan API Client
const solscanClient = new APIClient('https://api-v2.solscan.io', {
    'accept': 'application/json, text/plain, */*',
    'origin': 'https://solscan.io',
    'referer': 'https://solscan.io/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36'
});

// DEX3 API Client
const dex3Client = new APIClient('https://api.dex3.ai', {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9',
    'access-control-allow-origin': '*',
    'Content-Type': 'application/json',
    'Cookie': '_hjSessionUser_6397832=eyJpZCI6IjJkZjEwZDc3LWJkNzQtNTQwZi1iZWI2LThkZTc5OTQ1NDBmOCIsImNyZWF0ZWQiOjE3NTEzNjcyMzQ2NDIsImV4aXN0aW5nIjp0cnVlfQ==; _ga=GA1.1.1282429955.1751367234; refreshToken=s%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NCwiamlkIjoiMzU2M2M2ZmEtMzg3Yi00NTU4LThiMGQtNzk1YTkxMzgxNGJjIiwicmVmcmVzaFRva2VuIjp0cnVlLCJpYXQiOjE3NTU2NzEwMzMsImV4cCI6MTc1Njk2NzAzM30.5c7Z1yrKhHGF_2imhfH3o4yakBGkzxcI2MKgFhhZC_M.NX3wLuO5bz4clVls3XSxx68JdQcaUyoMYLDwVKf9SJA; _hjSession_6397832=eyJpZCI6IjYzY2MzMGMyLTlhNzgtNDJkMC1hYzE2LTMwNzZhYTU3ODJiMiIsImMiOjE3NTU2ODI1NTg4ODAsInMiOjEsInIiOjEsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MH0=; authorization=s%3ABearer%20eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NCwiamlkIjoiMzU2M2M2ZmEtMzg3Yi00NTU4LThiMGQtNzk1YTkxMzgxNGJjIiwiaWF0IjoxNzU1Njg1MzYwLCJleHAiOjE3NTU2ODg5NjB9.PKP9uX_7QyGi15C29FxZGQaWyiK0GHOgUQwULTwmNpQ.C%2BSyKzPUUBw7BLyrqDqapWflWqhWUZvnRHR79vstguE; AWSALB=KjcB0zD6g4Z+B82EGie+pJL0u8ld6th3LUR2vNa1fU31sKJY/jaI++4do96iTw65a5wM7EDs8l77673HsJWYr6Clcs07Zm6EE0Z/TW7d+9ea7u6//j9TF8yCffT3; AWSALBCORS=KjcB0zD6g4Z+B82EGie+pJL0u8ld6th3LUR2vNa1fU31sKJY/jaI++4do96iTw65a5wM7EDs8l77673HsJWYr6Clcs07Zm6EE0Z/TW7d+9ea7u6//j9TF8yCffT3; _ga_ME8GZB3L70=GS2.1.s1755681907%24o167%24g1%24t1755685361%24j57%24l0%24h0',
    'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NCwiamlkIjoiMzU2M2M2ZmEtMzg3Yi00NTU4LThiMGQtNzk1YTkxMzgxNGJjIiwiaWF0IjoxNzU1Njg1MzYwLCJleHAiOjE3NTU2ODg5NjB9.PKP9uX_7QyGi15C29FxZGQaWyiK0GHOgUQwULTwmNpQ',
    'origin': 'https://beta.dex3.ai',
    'referer': 'https://beta.dex3.ai/',
    'priority': 'u=1, i',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
});

// === API SERVICES ===
class SolscanService {
    static async getTokenInfo(address) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/account?address=${address}&view_as=token`
        });
    }

    static async getHolderCount(address) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/token/holder/total?address=${address}`
        });
    }

    static async getPoolsInfo(address) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/token/pools?page=1&page_size=100&token[]=${address}`
        });
    }
}

class Dex3Service {
    static async getTokenBasicInfo(address) {
        return await dex3Client.request({
            method: 'POST',
            url: '/v2/token-detail/basic-info',
            data: { address }
        });
    }

    static async getHoldersInfo(address) {
        const headers = {
            ...dex3Client.client.defaults.headers,
            'clienttimestamp': Date.now().toString()
        };

        return await dex3Client.request({
            method: 'POST',
            url: '/v2/token-detail/holders',
            data: { address, tag: "all" },
            headers
        });
    }
}

// === DATA PROCESSORS ===
class DataProcessor {
    static processSolscanData(tokenResponse, holderResponse, poolsResponse) {
        const tokenData = tokenResponse?.data;
        const metadata = tokenResponse?.metadata;
        const pools = poolsResponse?.data || [];
        
        if (!tokenData) {
            console.log("⚠️ Không tìm thấy dữ liệu token từ Solscan API");
            return null;
        }
        
        // Calculate aggregated pool data
        const poolStats = this.calculatePoolStats(pools);
        
        // Safely extract token info with fallbacks
        const tokenInfo = tokenData.tokenInfo || {};
        const metadataData = tokenData.metadata?.data || {};
        const ownExtensions = tokenInfo.ownExtensions || {};
        
        // Calculate token metrics with safe fallbacks
        const decimals = tokenInfo.decimals || 0;
        const rawSupply = tokenInfo.supply ? parseInt(tokenInfo.supply) : 0;
        const totalSupply = decimals > 0 ? rawSupply / Math.pow(10, decimals) : rawSupply;
        const price = metadata?.tokens?.[CONFIG.TOKEN_ADDRESS]?.price_usdt || 0;
        
        return {
            name: metadataData.name || 'N/A',
            symbol: metadataData.symbol || 'N/A',
            description: ownExtensions.description || 'N/A',
            website: ownExtensions.website || 'N/A',
            twitter: ownExtensions.twitter || 'N/A',
            totalSupply,
            holderCount: holderResponse?.data || 0,
            price,
            liquidity: poolStats.totalTvl,
            marketCap: totalSupply * price,
            creator: metadataData.creators?.[0]?.address || 'N/A',
            createdAt: tokenInfo.created_time ? 
                new Date(tokenInfo.created_time * 1000).toISOString() : 'N/A',
            totalVolume24h: poolStats.totalVolume24h,
            numTraders24h: poolStats.numTraders24h,
            top10HoldingsPct: "N/A"
        };
    }

    static processDex3Data(basicInfoResponse, holdersResponse) {
        const basicInfo = basicInfoResponse?.data;
        
        if (!basicInfo) {
            console.log("⚠️ Không tìm thấy dữ liệu token từ DEX3 API");
            return null;
        }
        
        // Safe extraction with fallbacks
        const baseToken = basicInfo.baseToken || {};
        const tokenInfo = basicInfo.tokenInfo || {};
        const h24Data = basicInfo.data?.h24 || {};
        
        return {
            name: baseToken.name || 'N/A',
            symbol: baseToken.symbol || 'N/A',
            description: baseToken.description || 'N/A',
            website: baseToken.website || 'N/A',
            twitter: baseToken.twitter || 'N/A',
            totalSupply: basicInfo.totalSupply || 0,
            holderCount: basicInfo.holderCount || 0,
            price: basicInfo.price || 0,
            liquidity: basicInfo.liquidity || 0,
            marketCap: basicInfo.marketCap || 0,
            creator: tokenInfo.devs || 'N/A',
            createdAt: basicInfo.createdAt || 'N/A',
            totalVolume24h: h24Data.volume || 0,
            numTraders24h: h24Data.traders || 0,
            top10HoldingsPct: this.calculateTop10Holdings(holdersResponse)
        };
    }

    static calculatePoolStats(pools = []) {
        return pools.reduce((acc, pool) => ({
            totalVolume24h: acc.totalVolume24h + (pool.total_volume_24h || 0),
            totalTvl: acc.totalTvl + (pool.total_tvl || 0),
            numTraders24h: acc.numTraders24h + (pool.num_trader_24h || 0)
        }), { totalVolume24h: 0, totalTvl: 0, numTraders24h: 0 });
    }

    static calculateTop10Holdings(holdersResponse) {
        if (!holdersResponse) {
            return 'Không có dữ liệu holder';
        }

        // Tìm kiếm mảng dữ liệu holder
        let holdersData = null;
        if (holdersResponse.data?.data?.length) {
            holdersData = holdersResponse.data.data;
        } else if (holdersResponse.data?.length) {
            holdersData = holdersResponse.data;
        } else if (holdersResponse.holders?.length) {
            holdersData = holdersResponse.holders;
        } else if (Array.isArray(holdersResponse)) {
            holdersData = holdersResponse;
        }

        if (!holdersData?.length) {
            return 'Không có dữ liệu holder';
        }

        const excludedWallets = [
            '********************************************'
        ];

        const filteredHolders = holdersData.filter(holder =>
            !excludedWallets.includes(holder.address)
        );

        const top10Holders = filteredHolders.slice(0, 10);

        // === THAY ĐỔI BẮT ĐẦU TẠI ĐÂY ===
        
        // Chỉ tính tổng % nắm giữ mà không in ra chi tiết từng ví
        const totalHoldings = top10Holders.reduce((accumulator, holder) => {
            const holdingPct = parseFloat(holder.holdingPct || holder.holding_pct || holder.percentage) || 0;
            return accumulator + holdingPct;
        }, 0); // Bắt đầu tính tổng từ 0

        // In ra dòng tổng kết
        console.log("\n📊 === PHÂN TÍCH TOP 10 HOLDERS ===");
        console.log(`💰 Tổng % nắm giữ của Top 10 (sau khi lọc): ${totalHoldings.toFixed(4)}%`);
        console.log("====================================\n");
        
        // === THAY ĐỔI KẾT THÚC TẠI ĐÂY ===

        return totalHoldings;
    }
}

// === DISPLAY UTILITIES ===
class DisplayUtils {
    static formatNumber(value, options = {}) {
        if (typeof value !== 'number' || isNaN(value)) return value;
        return value.toLocaleString(undefined, options);
    }

    static formatPercentage(value) {
        return typeof value === 'number' ? `${value.toFixed(2)}%` : value;
    }

    static createComparisonTable(solscanData, dex3Data) {
        const fields = [
            { key: 'name', label: 'Tên Token' },
            { key: 'symbol', label: 'Ký hiệu' },
            { key: 'website', label: 'Website' },
            { key: 'twitter', label: 'Twitter' },
            { key: 'totalSupply', label: 'Tổng cung', formatter: (v) => this.formatNumber(v) },
            { key: 'holderCount', label: 'Số người nắm giữ', formatter: (v) => this.formatNumber(v) },
            { key: 'price', label: 'Giá (USD)' },
            { key: 'marketCap', label: 'Market Cap (USD)', formatter: (v) => this.formatNumber(v, { maximumFractionDigits: 0 }) },
            { key: 'totalVolume24h', label: 'Tổng Volume 24h (USD)', formatter: (v) => this.formatNumber(v, { maximumFractionDigits: 0 }) },
            { key: 'liquidity', label: 'Tổng TVL (USD)', formatter: (v) => this.formatNumber(v, { maximumFractionDigits: 0 }) },
            { key: 'numTraders24h', label: 'Số Lượng Traders 24h', formatter: (v) => this.formatNumber(v) },
            { key: 'top10HoldingsPct', label: 'Top 10 Holding (%)', formatter: (v) => this.formatPercentage(v) },
            { key: 'creator', label: 'Địa chỉ người tạo' },
            { key: 'createdAt', label: 'Ngày tạo (UTC)', formatter: (v) => v?.replace('.000Z', 'Z') }
        ];

        return fields.reduce((table, field) => {
            const solscanValue = field.formatter ?
                field.formatter(solscanData[field.key]) :
                solscanData[field.key];
            const dex3Value = field.formatter ?
                field.formatter(dex3Data[field.key]) :
                dex3Data[field.key];

            table[field.label] = {
                'API Solscan': solscanValue,
                'API Dự án': dex3Value
            };
            return table;
        }, {});
    }

    static compareResults(solscanData, dex3Data) {
        if (!solscanData || !dex3Data) {
            return { totalDifference: 0, mismatches: [] };
        }

        const numericFields = [
            'totalSupply', 'holderCount', 'price', 'marketCap',
            'totalVolume24h', 'liquidity', 'numTraders24h'
        ];

        const stringFields = [
            'name', 'symbol', 'description', 'website', 'twitter', 'creator'
        ];

        let totalDifferences = 0;
        let totalFields = 0;
        const mismatches = [];

        // Compare numeric fields
        numericFields.forEach(field => {
            const solscanValue = parseFloat(solscanData[field]) || 0;
            const dex3Value = parseFloat(dex3Data[field]) || 0;

            if (solscanValue > 0 || dex3Value > 0) {
                totalFields++;
                const maxValue = Math.max(solscanValue, dex3Value);
                const difference = Math.abs(solscanValue - dex3Value);
                const percentDiff = maxValue > 0 ? (difference / maxValue) * 100 : 0;

                totalDifferences += percentDiff;

                if (percentDiff >= 5) {
                    mismatches.push({
                        field,
                        type: 'numeric',
                        solscanValue,
                        dex3Value,
                        percentDiff: percentDiff.toFixed(2)
                    });
                }
            }
        });

        // Compare string fields
        stringFields.forEach(field => {
            const solscanValue = String(solscanData[field] || '').trim();
            const dex3Value = String(dex3Data[field] || '').trim();

            if (solscanValue !== 'N/A' || dex3Value !== 'N/A') {
                totalFields++;

                if (solscanValue !== dex3Value) {
                    totalDifferences += 100; // String mismatch counts as 100% difference

                    mismatches.push({
                        field,
                        type: 'string',
                        solscanValue,
                        dex3Value,
                        percentDiff: '100.00'
                    });
                }
            }
        });

        const averageDifference = totalFields > 0 ? totalDifferences / totalFields : 0;

        return {
            totalDifference: averageDifference,
            mismatches,
            totalFields
        };
    }

    static logMismatchSummary(comparison) {
        // Filter mismatches to only show those with >5% difference
        const significantMismatches = comparison.mismatches.filter(mismatch =>
            parseFloat(mismatch.percentDiff) > 5
        );

        if (significantMismatches.length > 0) {
            console.log("\n⚠️ === CÁC TRƯỜNG DỮ LIỆU SAI LỆCH TRÊN 5% ===");

            significantMismatches.forEach((mismatch, index) => {
                const fieldNames = {
                    'name': 'Tên Token',
                    'symbol': 'Ký hiệu',
                    'description': 'Mô tả',
                    'website': 'Website',
                    'twitter': 'Twitter',
                    'totalSupply': 'Tổng cung',
                    'holderCount': 'Số người nắm giữ',
                    'price': 'Giá (USD)',
                    'marketCap': 'Market Cap (USD)',
                    'totalVolume24h': 'Tổng Volume 24h (USD)',
                    'liquidity': 'Tổng TVL (USD)',
                    'numTraders24h': 'Số Lượng Traders 24h',
                    'creator': 'Địa chỉ người tạo'
                };

                console.log(`${index + 1}. ${fieldNames[mismatch.field] || mismatch.field}: Chênh lệch ${mismatch.percentDiff}%`);
            });

            console.log("===========================================\n");
        }
    }

    static displayResults(solscanData, dex3Data) {
        console.log(`\n📊 Kết quả phân tích token: ${CONFIG.TOKEN_ADDRESS}\n`);

        // Display comparison table if we have data from at least one source
        if (solscanData || dex3Data) {
            // Use actual data or fallback to empty structure
            const solscanDataSafe = solscanData || this.createEmptyDataStructure();
            const dex3DataSafe = dex3Data || this.createEmptyDataStructure();

            const comparisonTable = this.createComparisonTable(solscanDataSafe, dex3DataSafe);
            console.log("--- Bảng So Sánh Dữ Liệu Token ---");
            console.table(comparisonTable);

            // Perform comparison analysis
            const comparison = this.compareResults(solscanDataSafe, dex3DataSafe);

            // Log mismatch summary if significant differences found
            this.logMismatchSummary(comparison);

            // Display descriptions
            console.log("\n--- So Sánh Mô Tả Đầy Đủ ---");
            console.log("\n[API Solscan]");
            console.log(solscanDataSafe.description || "(Không có mô tả)");
            console.log("\n[API Dự án]");
            console.log(dex3DataSafe.description || "(Không có mô tả)");
            console.log("\n---------------------------------");
        } else {
            console.log("❌ Không có dữ liệu để hiển thị");
        }
    }

    static createEmptyDataStructure() {
        return {
            name: 'N/A',
            symbol: 'N/A',
            description: 'N/A',
            website: 'N/A',
            twitter: 'N/A',
            totalSupply: 0,
            holderCount: 0,
            price: 0,
            liquidity: 0,
            marketCap: 0,
            creator: 'N/A',
            createdAt: 'N/A',
            totalVolume24h: 0,
            numTraders24h: 0,
            top10HoldingsPct: 'N/A'
        };
    }
}

// === MAIN APPLICATION ===
class TokenAnalyzer {
    static async analyze(tokenAddress = CONFIG.TOKEN_ADDRESS) {
        try {
            console.log(`🔄 Bắt đầu phân tích token: ${tokenAddress}...`);

            // Fetch all data concurrently
            console.log("📡 Đang lấy dữ liệu từ các API...");
            const [
                solscanTokenResponse,
                solscanHolderResponse,
                solscanPoolsResponse,
                dex3BasicInfoResponse,
                dex3HoldersResponse
            ] = await Promise.all([
                SolscanService.getTokenInfo(tokenAddress),
                SolscanService.getHolderCount(tokenAddress),
                SolscanService.getPoolsInfo(tokenAddress),
                Dex3Service.getTokenBasicInfo(tokenAddress),
                Dex3Service.getHoldersInfo(tokenAddress)
            ]);

            console.log("✅ Lấy dữ liệu thành công, đang xử lý...");

            // Process data
            const solscanData = DataProcessor.processSolscanData(
                solscanTokenResponse,
                solscanHolderResponse,
                solscanPoolsResponse
            );

            const dex3Data = DataProcessor.processDex3Data(
                dex3BasicInfoResponse,
                dex3HoldersResponse
            );

            // Display results
            DisplayUtils.displayResults(solscanData, dex3Data);

            console.log("✅ Phân tích hoàn tất!");
            return { solscanData, dex3Data };

        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }

    static handleError(error) {
        console.error("❌ Đã xảy ra lỗi!");
        
        if (error.response) {
            console.error(`API Error: ${error.response.status} ${error.response.statusText}`);
            console.error(`URL: ${error.config?.url}`);
            if (error.response.data) {
                console.error('Response:', JSON.stringify(error.response.data, null, 2));
            }
        } else if (error.request) {
            console.error('Network Error: Không nhận được phản hồi từ server');
        } else {
            console.error('Error:', error.message);
        }
    }
}

// === EXECUTION ===
if (require.main === module) {
    TokenAnalyzer.analyze().catch(() => process.exit(1));
}

module.exports = { TokenAnalyzer, CONFIG };